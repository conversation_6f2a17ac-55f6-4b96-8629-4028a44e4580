# API Cập Nhật Thông Tin Người Dùng

## Tổng quan
API này cho phép người dùng đã đăng nhập cập nhật thông tin profile củ<PERSON> họ, bao gồ<PERSON> họ tên, <PERSON><PERSON> đại diện và mật khẩu.

## Endpoint
```
PUT /auth/update-profile
GET /auth/update-profile
```

## Authentication
- **Required**: JWT Token trong header
- **Header**: `Authorization: Bearer <access_token>`

## Các trường có thể cập nhật

### 1. <PERSON><PERSON> tên (fullname)
- **Type**: String
- **Required**: Không (partial update)
- **Validation**: 
  - Không được để trống
  - Tối thiểu 2 ký tự
  - Tối đa 255 ký tự

### 2. Ảnh đại diện (avatar)
- **Type**: File (multipart/form-data)
- **Required**: <PERSON>hông
- **Validation**:
  - <PERSON><PERSON><PERSON> thước tối đa: 5MB
  - <PERSON><PERSON>nh dạng: JPEG, PNG, GIF

### 3. <PERSON><PERSON><PERSON>h<PERSON> (password)
- **Type**: String
- **Required**: Không
- **Validation**:
  - Tối thiểu 6 ký tự
  - Yêu cầu `current_password` khi thay đổi

### 4. Mật khẩu hiện tại (current_password)
- **Type**: String
- **Required**: Khi thay đổi mật khẩu
- **Validation**: Phải khớp với mật khẩu hiện tại

## Request Examples

### 1. Cập nhật họ tên
```bash
curl -X PUT http://localhost:8000/auth/update-profile \
  -H "Authorization: Bearer <access_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "fullname": "Nguyễn Văn A"
  }'
```

### 2. Cập nhật ảnh đại diện
```bash
curl -X PUT http://localhost:8000/auth/update-profile \
  -H "Authorization: Bearer <access_token>" \
  -F "avatar=@/path/to/image.jpg"
```

### 3. Thay đổi mật khẩu
```bash
curl -X PUT http://localhost:8000/auth/update-profile \
  -H "Authorization: Bearer <access_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "password": "new_password_123",
    "current_password": "old_password_123"
  }'
```

### 4. Cập nhật nhiều trường cùng lúc
```bash
curl -X PUT http://localhost:8000/auth/update-profile \
  -H "Authorization: Bearer <access_token>" \
  -F "fullname=Nguyễn Văn B" \
  -F "avatar=@/path/to/image.jpg" \
  -F "password=new_password_123" \
  -F "current_password=old_password_123"
```

### 5. Lấy thông tin profile hiện tại
```bash
curl -X GET http://localhost:8000/auth/update-profile \
  -H "Authorization: Bearer <access_token>"
```

## Response Format

### Success Response (200 OK)
```json
{
  "success": true,
  "message": "Cập nhật thông tin thành công",
  "data": {
    "id": 1,
    "email": "<EMAIL>",
    "fullname": "Nguyễn Văn A",
    "avatar": "/media/avatars/avatar.jpg",
    "role": "USER"
  }
}
```

### Validation Error (400 Bad Request)
```json
{
  "success": false,
  "message": "Dữ liệu không hợp lệ",
  "errors": {
    "fullname": ["Họ tên phải có ít nhất 2 ký tự."],
    "current_password": ["Mật khẩu hiện tại không đúng."]
  }
}
```

### Server Error (500 Internal Server Error)
```json
{
  "success": false,
  "message": "Có lỗi xảy ra khi cập nhật thông tin",
  "error": "Error details"
}
```

## Validation Rules

### Họ tên (fullname)
- ✅ "Nguyễn Văn A" - Hợp lệ
- ❌ "" - Không được để trống
- ❌ "A" - Quá ngắn (< 2 ký tự)

### Ảnh đại diện (avatar)
- ✅ image.jpg (< 5MB) - Hợp lệ
- ❌ large_image.jpg (> 5MB) - Quá lớn
- ❌ document.pdf - Sai định dạng

### Mật khẩu (password)
- ✅ "password123" - Hợp lệ
- ❌ "123" - Quá ngắn (< 6 ký tự)
- ❌ Không có current_password - Thiếu mật khẩu hiện tại

## Security Features

1. **JWT Authentication**: Yêu cầu token hợp lệ
2. **Password Verification**: Xác thực mật khẩu hiện tại trước khi thay đổi
3. **File Validation**: Kiểm tra kích thước và định dạng file
4. **Input Sanitization**: Làm sạch dữ liệu đầu vào
5. **Partial Updates**: Chỉ cập nhật các trường được gửi

## Error Codes

| Code | Message | Description |
|------|---------|-------------|
| 200 | Success | Cập nhật thành công |
| 400 | Bad Request | Dữ liệu không hợp lệ |
| 401 | Unauthorized | Token không hợp lệ hoặc hết hạn |
| 500 | Internal Server Error | Lỗi server |

## Notes

1. API hỗ trợ **partial updates** - chỉ cần gửi các trường muốn cập nhật
2. Email không thể thay đổi thông qua API này
3. Khi upload file, sử dụng `multipart/form-data`
4. Khi gửi JSON, sử dụng `application/json`
5. Token JWT có thời hạn 8 giờ (có thể thay đổi trong settings)
