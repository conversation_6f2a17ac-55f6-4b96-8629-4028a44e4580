"""
Test cases cho Dashboard Chart API với cấu trúc mới
"""
import json
from datetime import datetime, timedelta
from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APIClient
from rest_framework import status
from rest_framework_simplejwt.tokens import RefreshToken
from cls_backend.models import User
from chatbot.models import Conversation, ChatMessage
from request_log.models import RequestLog
from organization.models import Organization, OrganizationMember


class DashboardChartAPITest(TestCase):
    def setUp(self):
        """Thiết lập dữ liệu test"""
        self.client = APIClient()
        
        # Tạo superuser
        self.superuser = User.objects.create_user(
            email='<EMAIL>',
            password='adminpass123',
            fullname='Admin User',
            is_superuser=True,
            is_staff=True
        )
        
        # Tạo user thường
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='userpass123',
            fullname='Normal User'
        )
        
        # Tạo JWT token cho superuser
        refresh = RefreshToken.for_user(self.superuser)
        self.access_token = str(refresh.access_token)
        
        # Set authentication header
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.access_token}')
        
        # Tạo dữ liệu test
        self.create_test_data()

    def create_test_data(self):
        """Tạo dữ liệu test cho chart"""
        base_date = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        
        # Tạo users trong 5 ngày gần đây
        for i in range(5):
            day = base_date - timedelta(days=i)
            for j in range(i + 1):  # Ngày gần nhất có nhiều user hơn
                User.objects.create_user(
                    email=f'testuser_{i}_{j}@example.com',
                    password='testpass123',
                    fullname=f'Test User {i}_{j}',
                    created_time=day + timedelta(hours=j)
                )

    def test_chart_structure_short_period(self):
        """Test cấu trúc chart cho khoảng thời gian ngắn (≤ 10 ngày)"""
        # Tạo URL với khoảng thời gian 5 ngày
        from_date = (datetime.now() - timedelta(days=4)).isoformat()
        to_date = datetime.now().isoformat()
        
        url = f'/dashboard/?from_date={from_date}&to_date={to_date}'
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        
        # Kiểm tra các trường đã bị loại bỏ
        self.assertNotIn('chart_start_dates', data)
        self.assertNotIn('chart_end_dates', data)
        
        # Kiểm tra cấu trúc mới của chart_new_users
        self.assertIn('chart_new_users', data)
        self.assertIsInstance(data['chart_new_users'], list)
        
        if data['chart_new_users']:
            chart_item = data['chart_new_users'][0]
            
            # Kiểm tra các trường bắt buộc cho dữ liệu từng ngày
            required_fields = ['date', 'day_name', 'formatted_date', 'is_weekend', 'count', 'label']
            for field in required_fields:
                self.assertIn(field, chart_item)
            
            # Kiểm tra kiểu dữ liệu
            self.assertIsInstance(chart_item['date'], str)
            self.assertIsInstance(chart_item['day_name'], str)
            self.assertIsInstance(chart_item['formatted_date'], str)
            self.assertIsInstance(chart_item['is_weekend'], bool)
            self.assertIsInstance(chart_item['count'], int)
            self.assertIsInstance(chart_item['label'], str)
            
            # Kiểm tra định dạng ngày
            self.assertRegex(chart_item['date'], r'^\d{4}-\d{2}-\d{2}$')
            self.assertRegex(chart_item['formatted_date'], r'^\d{2}/\d{2}/\d{4}$')

    def test_chart_structure_long_period(self):
        """Test cấu trúc chart cho khoảng thời gian dài (> 10 ngày)"""
        # Tạo URL với khoảng thời gian 20 ngày
        from_date = (datetime.now() - timedelta(days=19)).isoformat()
        to_date = datetime.now().isoformat()
        
        url = f'/dashboard/?from_date={from_date}&to_date={to_date}'
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        
        # Kiểm tra các trường đã bị loại bỏ
        self.assertNotIn('chart_start_dates', data)
        self.assertNotIn('chart_end_dates', data)
        
        # Kiểm tra cấu trúc mới của chart_new_users
        self.assertIn('chart_new_users', data)
        self.assertIsInstance(data['chart_new_users'], list)
        
        if data['chart_new_users']:
            chart_item = data['chart_new_users'][0]
            
            # Kiểm tra các trường bắt buộc cho dữ liệu khoảng thời gian
            required_fields = [
                'start_date', 'end_date', 'formatted_start_date', 'formatted_end_date',
                'period_days', 'period_label', 'count', 'label', 'average_per_day'
            ]
            for field in required_fields:
                self.assertIn(field, chart_item)
            
            # Kiểm tra kiểu dữ liệu
            self.assertIsInstance(chart_item['start_date'], str)
            self.assertIsInstance(chart_item['end_date'], str)
            self.assertIsInstance(chart_item['period_days'], int)
            self.assertIsInstance(chart_item['period_label'], str)
            self.assertIsInstance(chart_item['count'], int)
            self.assertIsInstance(chart_item['average_per_day'], float)
            
            # Kiểm tra định dạng ngày
            self.assertRegex(chart_item['start_date'], r'^\d{4}-\d{2}-\d{2}$')
            self.assertRegex(chart_item['end_date'], r'^\d{4}-\d{2}-\d{2}$')

    def test_all_chart_types_consistency(self):
        """Test tính nhất quán giữa các loại chart"""
        from_date = (datetime.now() - timedelta(days=4)).isoformat()
        to_date = datetime.now().isoformat()
        
        url = f'/dashboard/?from_date={from_date}&to_date={to_date}'
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        
        # Kiểm tra tất cả các chart có cùng cấu trúc
        chart_types = ['chart_new_users', 'chart_new_conversations', 'chart_active_users']
        
        for chart_type in chart_types:
            self.assertIn(chart_type, data)
            self.assertIsInstance(data[chart_type], list)
            
            if data[chart_type]:
                chart_item = data[chart_type][0]
                
                # Tất cả chart phải có các trường cơ bản
                basic_fields = ['count', 'label']
                for field in basic_fields:
                    self.assertIn(field, chart_item)

    def test_weekend_detection(self):
        """Test phát hiện cuối tuần"""
        # Tạo dữ liệu cho một tuần cụ thể
        monday = datetime(2024, 1, 15)  # Thứ 2
        sunday = monday + timedelta(days=6)  # Chủ nhật
        
        from_date = monday.isoformat()
        to_date = sunday.isoformat()
        
        url = f'/dashboard/?from_date={from_date}&to_date={to_date}'
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        
        chart_data = data['chart_new_users']
        
        # Kiểm tra 7 ngày trong tuần
        self.assertEqual(len(chart_data), 7)
        
        # Thứ 2-6 không phải cuối tuần, Thứ 7-CN là cuối tuần
        expected_weekends = [False, False, False, False, False, True, True]
        
        for i, item in enumerate(chart_data):
            self.assertEqual(item['is_weekend'], expected_weekends[i])

    def test_average_calculation(self):
        """Test tính toán trung bình cho khoảng thời gian dài"""
        # Tạo khoảng thời gian > 10 ngày
        from_date = (datetime.now() - timedelta(days=15)).isoformat()
        to_date = datetime.now().isoformat()
        
        url = f'/dashboard/?from_date={from_date}&to_date={to_date}'
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        
        chart_data = data['chart_new_users']
        
        for item in chart_data:
            # Kiểm tra average_per_day được tính đúng
            expected_average = round(item['count'] / item['period_days'], 2) if item['period_days'] > 0 else 0
            self.assertEqual(item['average_per_day'], expected_average)

    def test_label_format(self):
        """Test định dạng label"""
        from_date = (datetime.now() - timedelta(days=2)).isoformat()
        to_date = datetime.now().isoformat()
        
        url = f'/dashboard/?from_date={from_date}&to_date={to_date}'
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        
        # Kiểm tra label của từng loại chart
        chart_types = {
            'chart_new_users': 'Người dùng mới',
            'chart_new_conversations': 'Cuộc trò chuyện mới',
            'chart_active_users': 'Người dùng hoạt động'
        }
        
        for chart_type, expected_text in chart_types.items():
            chart_data = data[chart_type]
            if chart_data:
                for item in chart_data:
                    self.assertIn(expected_text, item['label'])

    def print_sample_response(self):
        """In ra response mẫu để kiểm tra"""
        from_date = (datetime.now() - timedelta(days=2)).isoformat()
        to_date = datetime.now().isoformat()
        
        url = f'/dashboard/?from_date={from_date}&to_date={to_date}'
        response = self.client.get(url)
        
        if response.status_code == 200:
            data = response.json()
            sample_data = {
                'chart_new_users': data.get('chart_new_users', [])[:2],
                'chart_new_conversations': data.get('chart_new_conversations', [])[:2],
                'chart_active_users': data.get('chart_active_users', [])[:2]
            }
            print("\n=== SAMPLE CHART DATA ===")
            print(json.dumps(sample_data, indent=2, ensure_ascii=False))


if __name__ == '__main__':
    import django
    from django.conf import settings
    from django.test.utils import get_runner
    
    django.setup()
    TestRunner = get_runner(settings)
    test_runner = TestRunner()
    failures = test_runner.run_tests(["dashboard.test_chart_api"])
