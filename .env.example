POSTGRES_USER=root
POSTGRES_PASSWORD=123456
POSTGRES_DB=cls
POSTGRES_PORT=5432
POSTGRES_HOST=db

OCR_HOST=https://cls-ocr.cmcati.vn
EXTRACT_OCR=/ocr/upload_ocr_large_file
OCR_CHECK_STATUS=/check_process_status
CLS_HOST=https://cls.cmcati.vn/api/v2/core
LEGAL_SEARCH_BY_QUERY=/legal_search/similar_norms/by_query
SEARCH_EFFECTIVE_TIME=/conflict/thoi_gian_hieu_luc/search
CHECK_EFFECTIVE_TIME=/conflict/thoi_gian_hieu_luc/check
THAM_QUYEN_BAN_HANH=/conflict/tham_quyen_ban_hanh
THAM_QUYEN_HINH_THUC=/conflict/tham_quyen_hinh_thuc
KEYWORDS_LOAI_VAN_BAN=/utils/keywords/loai_van_ban
KEYWORDS_CO_QUAN_BAN_HANH=/utils/keywords/co_quan_ban_hanh
KEYWORDS_CHU_DE_PHAP_DIEN=/utils/keywords/co_quan_ban_hanh
KEYWORDS_SUA_DOI_BO_SUNG=/utils/keywords/sua_doi_bo_sung
KEYWORDS_TRANG_THAI_HIEU_LUC=/utils/keywords/trang_thai_hieu_luc

AWS_S3_ENDPOINT_URL=
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_STORAGE_BUCKET_NAME=
AWS_STORAGE_BUCKET_NAME_DOC=cls-doc
REDIS_HOST=
REDIS_PORT=
REDIS_PASSWORD=
CELERY_REDIS_DB=
CHANNEL_REDIS_DB=

RBMQ_HOST=
RBMQ_PORT=
RBMQ_USER_NAME=
RBMQ_PASSWORD=

OCR_REDIS_HOST=
OCR_REDIS_PORT=
OCR_REDIS_PASSWORD=
OCR_REDIS_TTL=
OCR_REDIS_DB=

# 10 DEBUG, 20 INFO, 30 WARNING 40 CRITICAL
LOG_LEVEL=10

DOC_TO_DOCX=http://*********:30510/convert

EMAIL_HOST='smtp.gmail.com'
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER='<EMAIL>'  
EMAIL_HOST_PASSWORD='equp lbrg snts aiqs' 
DEFAULT_FROM_EMAIL='<EMAIL>'

FRONTEND_URL=http://localhost:4200
BACKEND_URL=http://localhost:8000

CONFLICT_TIMEOUT=300

ELK_USERNAME=
ELK_PASSWORD=
ELK_ENDPOINT=
ELK_DOCUMENT_INDEX=

IE_VBHC_URL=http://********:9816/ie/document_ie
ES_SEARCH_DIEU_KHOAN=law_terms
ES_DETAIL_DOCUMENT=law_documents_t4

SOFT_TASK_TIMEOUT=1000
HARD_TASK_TIMEOUT=1030