# Dashboard Chart API - C<PERSON><PERSON> nhật mới

## Tổng quan thay đổi

API `DashboardAPIView` đã đư<PERSON>c cập nhật để cung cấp thông tin chi tiết hơn cho các chart, loại bỏ các trường không cần thiết và cải thiện cấu trúc dữ liệu.

## C<PERSON>c thay đổi chính

### 🗑️ **Đã loại bỏ:**
- `chart_start_dates`: Không còn cần thiết
- `chart_end_dates`: Không còn cần thiết

### 🔄 **Đã cải thiện:**
- `chart_new_users`: Trả về object với thông tin đầy đủ
- `chart_new_conversations`: Trả về object với thông tin đầy đủ  
- `chart_active_users`: Trả về object với thông tin đầy đủ

## C<PERSON><PERSON> trú<PERSON> dữ liệu mới

### Tr<PERSON><PERSON><PERSON> hợp ≤ 10 ngày (dữ liệu từng ngày)

```json
{
  "chart_new_users": [
    {
      "date": "2024-01-15",
      "day_name": "Monday",
      "formatted_date": "15/01/2024",
      "is_weekend": false,
      "count": 25,
      "label": "Người dùng mới ngày 15/01"
    },
    {
      "date": "2024-01-16",
      "day_name": "Tuesday", 
      "formatted_date": "16/01/2024",
      "is_weekend": false,
      "count": 18,
      "label": "Người dùng mới ngày 16/01"
    }
  ],
  "chart_new_conversations": [
    {
      "date": "2024-01-15",
      "day_name": "Monday",
      "formatted_date": "15/01/2024", 
      "is_weekend": false,
      "count": 45,
      "label": "Cuộc trò chuyện mới ngày 15/01"
    }
  ],
  "chart_active_users": [
    {
      "date": "2024-01-15",
      "day_name": "Monday",
      "formatted_date": "15/01/2024",
      "is_weekend": false, 
      "count": 120,
      "label": "Người dùng hoạt động ngày 15/01"
    }
  ]
}
```

### Trường hợp > 10 ngày (chia thành 10 cụm)

```json
{
  "chart_new_users": [
    {
      "start_date": "2024-01-01",
      "end_date": "2024-01-03",
      "formatted_start_date": "01/01/2024",
      "formatted_end_date": "03/01/2024",
      "period_days": 3,
      "period_label": "01/01 - 03/01",
      "count": 75,
      "label": "Người dùng mới (01/01 - 03/01)",
      "average_per_day": 25.0
    },
    {
      "start_date": "2024-01-04", 
      "end_date": "2024-01-06",
      "formatted_start_date": "04/01/2024",
      "formatted_end_date": "06/01/2024",
      "period_days": 3,
      "period_label": "04/01 - 06/01",
      "count": 60,
      "label": "Người dùng mới (04/01 - 06/01)",
      "average_per_day": 20.0
    }
  ]
}
```

## Các trường dữ liệu

### Dữ liệu từng ngày (≤ 10 ngày)

| Trường | Kiểu | Mô tả | Ví dụ |
|--------|------|-------|-------|
| `date` | string | Ngày theo định dạng YYYY-MM-DD | "2024-01-15" |
| `day_name` | string | Tên thứ trong tuần (tiếng Anh) | "Monday" |
| `formatted_date` | string | Ngày định dạng VN | "15/01/2024" |
| `is_weekend` | boolean | Có phải cuối tuần không | false |
| `count` | integer | Số lượng | 25 |
| `label` | string | Nhãn hiển thị | "Người dùng mới ngày 15/01" |

### Dữ liệu theo khoảng thời gian (> 10 ngày)

| Trường | Kiểu | Mô tả | Ví dụ |
|--------|------|-------|-------|
| `start_date` | string | Ngày bắt đầu YYYY-MM-DD | "2024-01-01" |
| `end_date` | string | Ngày kết thúc YYYY-MM-DD | "2024-01-03" |
| `formatted_start_date` | string | Ngày bắt đầu định dạng VN | "01/01/2024" |
| `formatted_end_date` | string | Ngày kết thúc định dạng VN | "03/01/2024" |
| `period_days` | integer | Số ngày trong khoảng | 3 |
| `period_label` | string | Nhãn khoảng thời gian | "01/01 - 03/01" |
| `count` | integer | Tổng số lượng trong khoảng | 75 |
| `label` | string | Nhãn hiển thị | "Người dùng mới (01/01 - 03/01)" |
| `average_per_day` | float | Trung bình mỗi ngày | 25.0 |

## Lợi ích của cấu trúc mới

### 🎯 **Linh hoạt hơn:**
- Frontend có thể tự quyết định cách hiển thị dữ liệu
- Không bị ràng buộc bởi cấu trúc cũ với start_dates/end_dates

### 📊 **Thông tin phong phú:**
- Có thêm thông tin về thứ trong tuần, cuối tuần
- Có trung bình mỗi ngày cho khoảng thời gian dài
- Có label sẵn để hiển thị

### 🔧 **Dễ sử dụng:**
- Mỗi chart item là một object hoàn chỉnh
- Có thể dễ dàng map để tạo chart
- Hỗ trợ nhiều định dạng ngày tháng

## Ví dụ sử dụng Frontend

### React/JavaScript
```javascript
// Hiển thị chart cho dữ liệu từng ngày
const chartData = response.chart_new_users.map(item => ({
  x: item.date,
  y: item.count,
  label: item.label,
  isWeekend: item.is_weekend
}));

// Hiển thị chart cho dữ liệu khoảng thời gian
const chartData = response.chart_new_users.map(item => ({
  x: item.period_label,
  y: item.count,
  label: item.label,
  average: item.average_per_day
}));
```

### Chart.js
```javascript
const chartConfig = {
  type: 'line',
  data: {
    labels: response.chart_new_users.map(item => 
      item.date || item.period_label
    ),
    datasets: [{
      label: 'Người dùng mới',
      data: response.chart_new_users.map(item => item.count),
      backgroundColor: response.chart_new_users.map(item => 
        item.is_weekend ? 'rgba(255, 99, 132, 0.2)' : 'rgba(54, 162, 235, 0.2)'
      )
    }]
  }
};
```

## Migration Guide

### Từ cấu trúc cũ:
```javascript
// Cũ
const dates = response.chart_start_dates;
const counts = response.chart_new_users;
const chartData = dates.map((date, index) => ({
  x: date,
  y: counts[index]
}));
```

### Sang cấu trúc mới:
```javascript
// Mới
const chartData = response.chart_new_users.map(item => ({
  x: item.date || item.period_label,
  y: item.count,
  label: item.label
}));
```

## Backward Compatibility

⚠️ **Lưu ý**: Đây là breaking change. Frontend cần được cập nhật để sử dụng cấu trúc mới.

Các trường đã bị loại bỏ:
- `chart_start_dates`
- `chart_end_dates`

Các trường đã thay đổi:
- `chart_new_users`: từ array số → array object
- `chart_new_conversations`: từ array số → array object  
- `chart_active_users`: từ array số → array object
