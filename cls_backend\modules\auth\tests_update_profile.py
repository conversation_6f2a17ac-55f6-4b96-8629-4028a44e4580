"""
Test cases cho API cập nhật thông tin người dùng
"""
import tempfile
from django.test import TestCase
from django.urls import reverse
from django.core.files.uploadedfile import SimpleUploadedFile
from rest_framework.test import APIClient
from rest_framework import status
from rest_framework_simplejwt.tokens import RefreshToken
from cls_backend.models import User
from PIL import Image
import io


class UpdateUserProfileAPITest(TestCase):
    def setUp(self):
        """Thiết lập dữ liệu test"""
        self.client = APIClient()
        self.url = reverse('update-profile')  # Cần thêm name vào URL pattern
        
        # Tạo user test
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpassword123',
            fullname='Test User'
        )
        
        # Tạo JWT token
        refresh = RefreshToken.for_user(self.user)
        self.access_token = str(refresh.access_token)
        
        # Set authentication header
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.access_token}')

    def create_test_image(self):
        """Tạo file ảnh test"""
        image = Image.new('RGB', (100, 100), color='red')
        image_file = io.BytesIO()
        image.save(image_file, format='JPEG')
        image_file.seek(0)
        return SimpleUploadedFile(
            name='test_image.jpg',
            content=image_file.read(),
            content_type='image/jpeg'
        )

    def test_get_user_profile(self):
        """Test lấy thông tin profile"""
        response = self.client.get(self.url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        self.assertEqual(response.data['data']['email'], '<EMAIL>')
        self.assertEqual(response.data['data']['fullname'], 'Test User')

    def test_update_fullname_success(self):
        """Test cập nhật họ tên thành công"""
        data = {'fullname': 'Updated Name'}
        response = self.client.put(self.url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        self.assertEqual(response.data['data']['fullname'], 'Updated Name')
        
        # Kiểm tra database
        self.user.refresh_from_db()
        self.assertEqual(self.user.fullname, 'Updated Name')

    def test_update_fullname_invalid(self):
        """Test cập nhật họ tên không hợp lệ"""
        # Test họ tên quá ngắn
        data = {'fullname': 'A'}
        response = self.client.put(self.url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertFalse(response.data['success'])
        self.assertIn('fullname', response.data['errors'])

    def test_update_avatar_success(self):
        """Test cập nhật ảnh đại diện thành công"""
        image = self.create_test_image()
        data = {'avatar': image}
        response = self.client.put(self.url, data, format='multipart')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        
        # Kiểm tra database
        self.user.refresh_from_db()
        self.assertTrue(self.user.avatar)

    def test_update_password_success(self):
        """Test thay đổi mật khẩu thành công"""
        data = {
            'password': 'newpassword123',
            'current_password': 'testpassword123'
        }
        response = self.client.put(self.url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        
        # Kiểm tra mật khẩu mới
        self.user.refresh_from_db()
        self.assertTrue(self.user.check_password('newpassword123'))

    def test_update_password_wrong_current(self):
        """Test thay đổi mật khẩu với mật khẩu hiện tại sai"""
        data = {
            'password': 'newpassword123',
            'current_password': 'wrongpassword'
        }
        response = self.client.put(self.url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertFalse(response.data['success'])
        self.assertIn('current_password', response.data['errors'])

    def test_update_password_missing_current(self):
        """Test thay đổi mật khẩu thiếu mật khẩu hiện tại"""
        data = {'password': 'newpassword123'}
        response = self.client.put(self.url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertFalse(response.data['success'])
        self.assertIn('current_password', response.data['errors'])

    def test_update_password_too_short(self):
        """Test thay đổi mật khẩu quá ngắn"""
        data = {
            'password': '123',
            'current_password': 'testpassword123'
        }
        response = self.client.put(self.url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertFalse(response.data['success'])
        self.assertIn('password', response.data['errors'])

    def test_update_multiple_fields(self):
        """Test cập nhật nhiều trường cùng lúc"""
        image = self.create_test_image()
        data = {
            'fullname': 'New Full Name',
            'avatar': image,
            'password': 'newpassword123',
            'current_password': 'testpassword123'
        }
        response = self.client.put(self.url, data, format='multipart')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        
        # Kiểm tra database
        self.user.refresh_from_db()
        self.assertEqual(self.user.fullname, 'New Full Name')
        self.assertTrue(self.user.avatar)
        self.assertTrue(self.user.check_password('newpassword123'))

    def test_unauthorized_access(self):
        """Test truy cập không có token"""
        self.client.credentials()  # Remove authentication
        response = self.client.put(self.url, {}, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_invalid_token(self):
        """Test token không hợp lệ"""
        self.client.credentials(HTTP_AUTHORIZATION='Bearer invalid_token')
        response = self.client.put(self.url, {}, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_partial_update(self):
        """Test partial update - chỉ cập nhật một trường"""
        original_fullname = self.user.fullname
        
        # Chỉ cập nhật fullname, không thay đổi các trường khác
        data = {'fullname': 'Only Name Changed'}
        response = self.client.put(self.url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        
        # Kiểm tra chỉ fullname thay đổi
        self.user.refresh_from_db()
        self.assertEqual(self.user.fullname, 'Only Name Changed')
        self.assertEqual(self.user.email, '<EMAIL>')  # Không thay đổi

    def test_email_readonly(self):
        """Test email không thể thay đổi"""
        data = {
            'email': '<EMAIL>',
            'fullname': 'New Name'
        }
        response = self.client.put(self.url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Email không thay đổi
        self.user.refresh_from_db()
        self.assertEqual(self.user.email, '<EMAIL>')
        self.assertEqual(self.user.fullname, 'New Name')  # Fullname thay đổi


if __name__ == '__main__':
    import django
    from django.conf import settings
    from django.test.utils import get_runner
    
    django.setup()
    TestRunner = get_runner(settings)
    test_runner = TestRunner()
    failures = test_runner.run_tests(["cls_backend.modules.auth.tests_update_profile"])
