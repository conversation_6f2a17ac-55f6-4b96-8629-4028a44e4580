from rest_framework import serializers

from cls_backend.models import User


class UserSerializer(serializers.ModelSerializer):
    password = serializers.CharField(write_only=True)
    # token = serializers.ReadOnlyField()

    class Meta:
        model = User
        fields = ['id', 'email', 'password', 'avatar', 'fullname', 'is_staff']

    def create(self, validated_data):
        return User.objects.create_user(**validated_data)

    def to_representation(self, instance):
        data = super().to_representation(instance)
        if data['is_staff']:
            data['role'] = 'ADMIN'
        else:
            data['role'] = 'USER'
        data.pop('is_staff')
        return data


class ChangePasswordSerializer(serializers.Serializer):
    model = User

    """
    Serializer for password change endpoint.
    """
    current_password = serializers.CharField(required=True)
    new_password = serializers.CharField(required=True)
    
class PasswordResetSerializer(serializers.Serializer):
    email = serializers.EmailField(required=True)
    
class PasswordResetConfirmSerializer(serializers.Serializer):
    new_password = serializers.CharField(min_length=6, required=True)


class UpdateUserProfileSerializer(serializers.ModelSerializer):
    """
    Serializer chuyên dụng cho việc cập nhật thông tin profile người dùng
    """
    email = serializers.EmailField(read_only=True)  # Email không được phép thay đổi
    password = serializers.CharField(write_only=True, required=False)
    current_password = serializers.CharField(write_only=True, required=False)

    class Meta:
        model = User
        fields = ['id', 'email', 'fullname', 'avatar', 'password', 'current_password']
        extra_kwargs = {
            'id': {'read_only': True},
        }

    def validate_fullname(self, value):
        """Validate fullname field"""
        if not value or not value.strip():
            raise serializers.ValidationError("Họ tên không được để trống.")
        if len(value.strip()) < 2:
            raise serializers.ValidationError("Họ tên phải có ít nhất 2 ký tự.")
        if len(value.strip()) > 255:
            raise serializers.ValidationError("Họ tên không được vượt quá 255 ký tự.")
        return value.strip()

    def validate_avatar(self, value):
        """Validate avatar file"""
        if value:
            # Kiểm tra kích thước file (tối đa 5MB)
            if value.size > 5 * 1024 * 1024:
                raise serializers.ValidationError("Kích thước ảnh không được vượt quá 5MB.")

            # Kiểm tra định dạng file
            allowed_types = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif']
            if hasattr(value, 'content_type') and value.content_type not in allowed_types:
                raise serializers.ValidationError("Chỉ chấp nhận file ảnh định dạng JPEG, PNG, GIF.")

        return value

    def validate(self, attrs):
        """Validate toàn bộ dữ liệu"""
        # Nếu có password mới, phải có current_password
        if 'password' in attrs:
            if not attrs.get('current_password'):
                raise serializers.ValidationError({
                    'current_password': 'Vui lòng nhập mật khẩu hiện tại để thay đổi mật khẩu.'
                })

            # Kiểm tra mật khẩu hiện tại
            user = self.instance
            if not user.check_password(attrs['current_password']):
                raise serializers.ValidationError({
                    'current_password': 'Mật khẩu hiện tại không đúng.'
                })

            # Validate mật khẩu mới
            new_password = attrs['password']
            if len(new_password) < 6:
                raise serializers.ValidationError({
                    'password': 'Mật khẩu mới phải có ít nhất 6 ký tự.'
                })

        # Loại bỏ current_password khỏi attrs vì không cần lưu vào database
        attrs.pop('current_password', None)

        return attrs

    def update(self, instance, validated_data):
        """Cập nhật thông tin user"""
        # Xử lý password riêng
        password = validated_data.pop('password', None)

        # Cập nhật các trường khác
        for attr, value in validated_data.items():
            setattr(instance, attr, value)

        # Cập nhật password nếu có
        if password:
            instance.set_password(password)

        instance.save()
        return instance

    def to_representation(self, instance):
        """Customize response data"""
        data = super().to_representation(instance)

        # Thêm role field
        if instance.is_staff:
            data['role'] = 'ADMIN'
        else:
            data['role'] = 'USER'

        # Loại bỏ các field không cần thiết
        data.pop('password', None)
        data.pop('current_password', None)

        return data
